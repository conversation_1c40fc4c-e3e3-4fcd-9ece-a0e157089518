const User = require('../models/User');

const getDoctors = async (req, res) => {
  try {
    const doctors = await User.find(
      { role: 'doctor' },
      'name email specialization'  // Only return necessary fields
    );
    res.json(doctors);
  } catch (err) {
    console.error('Error fetching doctors:', err);
    res.status(500).json({ error: 'Failed to fetch doctors' });
  }
};

module.exports = {
  getDoctors
}; 