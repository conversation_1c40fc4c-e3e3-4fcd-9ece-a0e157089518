const Appointment = require("../models/Appointment");
const User = require("../models/User");

const bookAppointment = async (req, res) => {
  const { doctorId, date, time, type = 'consultation' } = req.body;
  const patientId = req.user._id; // Using _id from authenticated user

  try {
    // Validate doctor exists and is a doctor
    const doctor = await User.findOne({ _id: doctorId, role: 'doctor' });
    if (!doctor) {
      return res.status(400).json({ error: "Invalid doctor ID" });
    }

    // Check for existing appointment at same time
    const existingAppointment = await Appointment.findOne({
      doctorId,
      date,
      time,
      status: { $ne: 'rejected' } // Exclude rejected appointments
    });

    if (existingAppointment) {
      return res.status(400).json({ error: "Time slot not available" });
    }

    const appt = await Appointment.create({ doctorId, patientId, date, time, type });
    
    // Populate doctor details
    const populatedAppt = await Appointment.findById(appt._id)
      .populate('doctorId', 'name email')
      .populate('patientId', 'name email');

    res.status(201).json({ 
      message: "Appointment booked successfully", 
      appointment: populatedAppt 
    });
  } catch (err) {
    console.error("Booking error:", err);
    res.status(500).json({ error: "Appointment booking failed" });
  }
};

const getPatientAppointments = async (req, res) => {
  try {
    const appointments = await Appointment.find({ patientId: req.user._id })
      .populate('doctorId', 'name email')
      .populate('patientId', 'name email')
      .sort({ date: 1, time: 1 });
    res.json(appointments);
  } catch (err) {
    console.error("Fetch error:", err);
    res.status(500).json({ error: "Error fetching appointments" });
  }
};

const getDoctorAppointments = async (req, res) => {
  try {
    const appointments = await Appointment.find({ doctorId: req.user._id })
      .populate('doctorId', 'name email')
      .populate('patientId', 'name email')
      .sort({ date: 1, time: 1 });
    res.json(appointments);
  } catch (err) {
    console.error("Fetch error:", err);
    res.status(500).json({ error: "Error fetching doctor appointments" });
  }
};

const updateAppointmentStatus = async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  try {
    // Verify the appointment belongs to this doctor
    const appointment = await Appointment.findOne({ 
      _id: id, 
      doctorId: req.user._id 
    });

    if (!appointment) {
      return res.status(404).json({ error: "Appointment not found" });
    }

    const updated = await Appointment.findByIdAndUpdate(
      id, 
      { status }, 
      { new: true }
    ).populate('doctorId', 'name email')
     .populate('patientId', 'name email');

    res.json(updated);
  } catch (err) {
    console.error("Update error:", err);
    res.status(500).json({ error: "Status update failed" });
  }
};

module.exports = {
  bookAppointment,
  getPatientAppointments,
  getDoctorAppointments,
  updateAppointmentStatus
}; 