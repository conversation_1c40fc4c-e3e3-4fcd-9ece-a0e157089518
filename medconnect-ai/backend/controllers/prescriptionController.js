const Prescription = require("../models/Prescription");

const addPrescription = async (req, res) => {
  const { patientId, diagnosis, medicines, advice } = req.body;
  try {
    const newPresc = await Prescription.create({
      patientId,
      doctorId: req.user.id,
      diagnosis,
      medicines,
      advice
    });
    res.status(201).json(newPresc);
  } catch (err) {
    res.status(500).json({ error: "Failed to create prescription" });
  }
};

const getPatientPrescriptions = async (req, res) => {
  try {
    const prescriptions = await Prescription.find({ patientId: req.user.id })
      .populate('doctorId', 'name')
      .sort({ createdAt: -1 });
    res.json(prescriptions);
  } catch (err) {
    res.status(500).json({ error: "Failed to load prescriptions" });
  }
};

const getDoctorPrescriptions = async (req, res) => {
  try {
    const prescriptions = await Prescription.find({ doctorId: req.user.id })
      .populate('patientId', 'name')
      .sort({ createdAt: -1 });
    res.json(prescriptions);
  } catch (err) {
    res.status(500).json({ error: "Error fetching doctor's prescriptions" });
  }
};

module.exports = {
  addPrescription,
  getPatientPrescriptions,
  getDoctorPrescriptions
}; 